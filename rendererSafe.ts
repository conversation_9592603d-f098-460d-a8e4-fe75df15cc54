/**
 * This file should export only types/interfaces and LIMITED functions.
 * Since these types will be used in the Electron renderer thread,
 * it should not contain any nodejs specific code (and as little of actual code as possible).
 * Adding even a specific code in a function that is not called will cause the whole file to be bundled with nodejs dependencies,
 * and fail in electron (instanceof ScraperRun as an example)
 *
 * NOTE:
 * If you found yourself here because of an error in starting electron check if any of the imports you added
 * contain any logic that is not renderer safe.
 * What does that exactly mean?
 * Any native Node.js api call functions for example.
 */

export {Portal} from './src/types/Portal';
export {Source} from './src/types/Source';
export {PublicScraperConfiguration} from './src/publicInterfaces/PublicScraperConfiguration';
export {EventType, eventTypes} from './src/types/EventsTypes';
export {ScraperConfiguration, ScraperConfigurationStatus} from './src/configurations/ScraperConfiguration';
export {sourceToPortal, manualLoginPortalToSource} from './src/types/mappings';
export {SourceAccount} from './src/configurations/SourceAccount';
export {DateRange} from './src/types/DateRange';
export {Credentials} from './src/runs/credentials';
export {MicrosoftApiSetupData} from './src/types/MicrosoftApiSetupData';
export {SourceSideOrganization, LoginResult, ManualLoginDetailsResult} from './src/processes/types';
export * as errorMessages from './src/types/errorMessages';
export {DEFAULT_DAILY_SCHEDULE, Schedule} from './src/cron/Schedule';
