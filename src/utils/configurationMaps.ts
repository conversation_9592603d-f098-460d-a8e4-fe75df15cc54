import {ScraperConfiguration, ScraperConfigurationStatus} from '../configurations/ScraperConfiguration';
import {RunManager} from '../runs/RunManager';

export const configWithDynamicStatuses = (config: ScraperConfiguration, runManager: RunManager): ScraperConfiguration => {
    const runningRun = runManager.getRunningRunForSource(config.source);
    const queuedRun = runManager.getScheduledRunsForSource(config.source);
    let status = config.status;
    if (runningRun) {
        status = ScraperConfigurationStatus.RUNNING_SCRAPE;
    } else if (queuedRun) {
        status = ScraperConfigurationStatus.SCHEDULED;
    }

    return {
        ...config,
        status,
        run: runningRun
    };
};
