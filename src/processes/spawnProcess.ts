import {ChildProcessWithoutNullStreams, spawn} from 'child_process';
import {errorType} from '../configurations/errorType';
import {censorSensitiveMessage} from '../telemetry/filterSensitiveData';
import {ScraperServiceLogLevel} from '../telemetry/scraperService/iScraperServiceClient';
import * as telemetry from '../telemetry/telemetry';
import {Command, Source} from '../types';
import {Deferred} from '../utils/Deferred';
import {registerProcess} from './processManager';
import {BinaryResult} from './types';
import {EntryContext, EntryValidationError} from './types/entries/BaseEntry';
import {EntryFactory} from './types/EntryFactory';
import {ScraperLibError} from './types/errors';
import {Execution} from './types/Execution';

// TODO operationId is passed here temporarily, it should be handled in a better way, perhaps in binaryProxy
export async function spawnProcess<ResultType extends BinaryResult>(execPath: string, params: readonly string[], operationId: string): Promise<Execution<ResultType>> {
    const command = params[0] as Command;
    const source = findParamValue('--source', params) as Source;
    let childProcess: ChildProcessWithoutNullStreams | null = null;
    try {
        childProcess = spawn(execPath, params, {
            windowsHide: true
        });
    } catch (error) {
        const scraperError = new ScraperLibError(errorType.DEPENDENCIES_SYNC_ERROR, 'error');
        telemetry.exception(error, false, {execPath, source, command}, operationId, source);
        throw scraperError;
    }
    telemetry.trace(`Started ${command} for ${source} (PID:${childProcess.pid})`, {logLevel: ScraperServiceLogLevel.INFO, operationId, source});
    const deferred = new Deferred<Execution<ResultType>>();
    registerProcess(childProcess.pid!, source, command, deferred);
    const deferredResult = new Deferred<ResultType>();
    const execution = new Execution<ResultType>(childProcess.pid!, deferredResult.promise);
    deferred.resolve(execution);

    async function handleData(data: any) {
        const lines = data.split('\n');
        for (const line of lines) {
            if (!line) {
                continue;
            }
            try {
                const json = {source, ...JSON.parse(line)};
                if (json?.version == 2) {
                    const context: EntryContext = {
                        command,
                        operationId,
                        childProcessPid: childProcess?.pid,
                        execution,
                        deferredResult
                    };
                    const entry = await EntryFactory.create(json);
                    await entry.handle(context);
                } else {
                    telemetry.trace(`Unsupported version of ${command} stdout entry. Got: ${json?.version}`, {
                        logLevel: ScraperServiceLogLevel.ERROR,
                        operationId,
                        source
                    });
                }
            } catch (error) {
                if (error instanceof EntryValidationError) {
                    telemetry.trace(`Validation failed for ${command} entry: ${error.errors.join(', ')}`, {logLevel: ScraperServiceLogLevel.ERROR, operationId, source});
                }
                telemetry.exception(error, false, {line: censorSensitiveMessage(line), source, command, operation: 'handleData'}, operationId, source);

                return; // SHOULD WE RETURN HERE?
            }
        }
    }

    childProcess.stdout.setEncoding('utf8');
    childProcess.stdout.on('data', handleData);

    childProcess.stderr.setEncoding('utf8');
    childProcess.stderr.on('data', handleData);

    childProcess.on('spawn', async () => {
        // NOTE This is to prevent a situation in which the child process is force killed before it has a chance to start properly
        // In reality, I'm not sure if that can happen with current setup (node v14) as the spawn event was introduced in v14.17.0. This should work when we update electron to newer version
        // TODO verify few things:
        // 1. is the execution already created when we get here?
        // 2. is the `spawn` event emitted on current version of node
        // 3. check if it solves the issue of the child process running in the background when we update electron version
        if (execution?.forceKilled) {
            await execution.kill(operationId);
        }
    });

    childProcess.on('close', (code, signal) => {
        if (execution) {
            execution.exitCode = code || undefined;
            execution.exitSignal = signal || undefined;
        }

        if (!execution.output.length && !execution.forceKilled && code !== 0 && signal === null) {
            deferredResult.reject(
                new ScraperLibError(errorType.DEPENDENCY_EXECUTION_ERROR, 'error', {message: 'Binary did not return a result line. Check if it was initiated properly'})
            );
        }
    });

    childProcess.on('error', async (error) => {
        telemetry.exception(error, false, {source, command, operation: 'spawnProcess'}, operationId, source);
        deferredResult.reject(new ScraperLibError(errorType.DEPENDENCIES_SYNC_ERROR, 'error'));
    });

    return deferred.promise;
}

export const findParamValue = (paramName: string, paramsArray: readonly string[]): string | undefined => {
    const paramString = paramsArray.find((paramString) => paramString.includes(paramName));
    return stringParamToObject(paramString ?? '').value;
};

const stringParamToObject = (paramString: string): {key: string; value?: string} => {
    const [key, value] = paramString.replace(/^--/g, '').split(/=(.*)/);
    return {key, value};
};

/**
 * @deprecated
 * This should be removed once old entry handling logic is removed (aka old entry handling mechanism is removed)
 **/
export function sendJsonAsTrace(json: any, operationId: string, source: Source) {
    const message = json.data ? `${json.message} ${JSON.stringify(json.data)}` : json.message || 'Message not present'; //TODO message should not be optional but sometimes it doesn't exist
    telemetry.trace(message, {logLevel: json.logLevel, operationId, source, additionalData: {metadata: json?.metadata, originId: json.originId}});
}
