import {IsBoolean, IsDateString, IsIn, IsOptional, IsString, ValidateNested} from 'class-validator';
import {Source} from '../../types/Source';

class SourceAuthResult {
    @IsString()
    id: string;

    @IsBoolean()
    @IsOptional()
    hasScrapeBlockingIssues?: boolean;
}

export class LoginResult extends SourceAuthResult {}
export class CheckSessionResult extends SourceAuthResult {}

export class ScrapeResult {
    @IsString()
    reportFileName: string;
    @IsDateString()
    startDate: string;
    @IsDateString()
    endDate: string;

    @IsString()
    source: Source;

    /** @deprecated */
    @IsString()
    platform: string;

    /** @deprecated */
    @IsString()
    subplatform: string;

    @IsBoolean()
    noData: boolean;
}

export class SourceSideOrganization {
    @IsString()
    id: string;

    @IsBoolean()
    @IsOptional()
    hasScrapeBlockingIssues?: boolean;

    @IsString()
    name: string;
}

class SuccessSelector {
    @IsString()
    @IsIn(['css', 'xpath'])
    type: 'xpath' | 'css';

    @IsString()
    value: string;
}

export class ManualLoginDetailsResult {
    /**
     * url that should be loaded for the user to manually log in
     */
    @IsString()
    url: string;

    /**
     * selector to automatically determine if the login process was successful
     */
    @ValidateNested()
    successSelector: SuccessSelector;
}

export type BinaryResult = ManualLoginDetailsResult | SourceSideOrganization[] | ScrapeResult[] | LoginResult | CheckSessionResult | null;
