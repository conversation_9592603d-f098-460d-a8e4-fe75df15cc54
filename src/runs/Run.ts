import {v4 as uuidv4} from 'uuid';
import {errorType} from '../configurations/errorType';
import {ScraperLibError} from '../processes/types/errors';
import {Deferred} from '../utils/Deferred';
import {retryAction} from '../utils/retryAction';
import {BaseContext} from './jobs/context';
import {Job, JobResult} from './jobs/Job';

export class Run<Context extends BaseContext = BaseContext, ResultType extends JobResult = JobResult> {
    //TODO make this use awaitableJob
    public readonly id: string;
    isDone = false;
    jobFinishedPromise: Deferred<ResultType[]>;
    public readonly priority: number;
    private currentJob?: Job;
    private killed = false;
    private failed = false;
    private results: ResultType[] = [];
    public jobs: Job[];
    public startDate: Date;
    context: Context;

    onScheduled?: () => Promise<void>;
    onStart?: () => Promise<void>;
    onSuccess?: () => Promise<void>;
    onFail?: (e: Error | any) => Promise<void>;
    onProgress?: (percentage: number) => void;
    onFinally?: () => Promise<void>;
    onKill?: () => Promise<void>;

    constructor(
        jobs: Job[],
        options: {
            priority?: number;
            context?: Omit<Context, 'operationId' | 'isShadowRun'>;
            onScheduled?: () => Promise<void>;
            onStart?: () => Promise<void>;
            onSuccess?: () => Promise<void>;
            onFail?: (e: any) => Promise<void>;
            onFinally?: () => Promise<void>;
            onProgress?: (percentage: number) => Promise<void>;
            onKill?: () => Promise<void>;
        } = {}
    ) {
        this.id = uuidv4();
        this.jobs = jobs;
        this.priority = options.priority ?? 0;
        this.context = {
            ...options.context,
            operationId: this.id,
            isShadowRun: !!options.context?.shadowModeTask
        } as Context;
        this.onScheduled = options.onScheduled;
        this.onStart = options.onStart;
        this.onSuccess = options.onSuccess;
        this.onFail = options.onFail;
        this.onFinally = options.onFinally;
        this.onProgress = options.onProgress;
        this.onKill = options.onKill;
        this.jobFinishedPromise = new Deferred<ResultType[]>();
        this.jobFinishedPromise.promise.catch(async (e) => {
            if (!(e instanceof OperationTerminated) && this.onFail) {
                await this.onFail(e);
            }
        });
        this.startDate = new Date();
    }

    public async executeAllJobs() {
        try {
            this.onStart && (await this.onStart());
            for (const job of this.jobs) {
                if (this.killed) {
                    return;
                }
                try {
                    this.currentJob = job;
                    const result = await retryAction({
                        target: () => job.execute(this.context),
                        ...job.retryOptions,
                        retryCondition: () => {
                            return !this.killed;
                        }
                    });
                    this.results.push(result);
                    await job.onSuccess(this.context);
                } catch (error) {
                    this.results = [];
                    if (this.killed) {
                        const jobKilledError = new ScraperLibError(errorType.JOB_STOPPED_BY_USER);
                        this.jobFinishedPromise.reject(jobKilledError);
                        throw jobKilledError;
                    }
                    this.failed = true;
                    await job.onFail(error, this.context);
                    this.jobFinishedPromise.reject(error);
                    throw error;
                } finally {
                    this.onProgress && this.onProgress(this.progress);
                }
            }
            this.isDone = true;
            this.onSuccess && (await this.onSuccess());
            this.jobFinishedPromise.resolve(this.results);
        } finally {
            this.onFinally && (await this.onFinally());
        }
    }

    public isActive() {
        return !(this.isDone || this.failed || this.killed);
    }

    public async kill() {
        this.killed = true;
        if (this.currentJob) {
            await this.currentJob.kill(this.context);
        }

        this.onKill && (await this.onKill());

        this.jobFinishedPromise.reject(new OperationTerminated());
    }

    public get progress(): number {
        if (this.isDone || this.failed || this.killed) {
            return 100;
        }

        return Math.round((this.processedItems / this.totalItems) * 100);
    }

    get processedItems(): number {
        if (this.isDone || this.failed || this.killed) {
            return this.totalItems;
        }

        return Number(this.isDone);
    }

    get totalItems(): number {
        return 1;
    }

    setProgressListener(onProgress: (percentage: number) => void) {
        this.onProgress = onProgress;
    }

    async getJobResults(): Promise<ResultType[]> {
        return this.jobFinishedPromise.promise;
    }
}

export class OperationTerminated extends Error {
    constructor() {
        super('Operation terminated');
    }
}
