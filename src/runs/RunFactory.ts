import {ScraperConfiguration, ScraperConfigurationStatus} from '../configurations/ScraperConfiguration';
import {SourceAccount} from '../configurations/SourceAccount';
import {BinaryProxy, CheckSessionParams, CommonParams, LoginParams, ScrapeParams} from '../dependencies/BinaryProxy';
import {DependenciesManager} from '../dependencies/DependenciesManager';
import {getFeatureFlags} from '../featureFlags/featureFlags';
import {CheckSessionResult, LoginResult, ManualLoginDetailsResult, SourceSideOrganization} from '../processes/types';
import {Storage} from '../storage/Storage';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import {TriggeredBy} from '../telemetry/scraperService/scraperServiceEvents';
import {Command, DateRange, ShadowModeTask, Source} from '../types';
import {sumDaysInDateRanges} from '../utils/datesUtils';
import {copySessionFileForShadowRun} from '../utils/session';
import {Credentials, sourceAccountToCredentials, sourceAccountToScrapeCredentials} from './credentials';
import {BaseContext, ScrapeRunContext} from './jobs/context';
import {EnsureValidSessionJob} from './jobs/EnsureValidSessionJob';
import {GetManualLoginDataJob} from './jobs/GetManualLoginDataJob';
import {GetSourceSideOrganizationsJob} from './jobs/GetSourceSideOrganizationsJob';
import {LoginRun} from './LoginRun';
import {getSourcePriority} from './PriorityQueue';
import {Run} from './Run';
import {ScrapeRun} from './ScrapeRun';
import {UpdateStatus, getShadowModeStatusHelpers, getStatusHelpers} from './status';

export class RunFactory {
    constructor(
        private readonly mainDir: string,
        private readonly storage: Storage,
        private readonly dependenciesManager: DependenciesManager,
        private readonly binaryProxy: BinaryProxy,
        private readonly s2Client: iScraperServiceClient
    ) {}

    async createScrapeRun(
        source: Source,
        dateRanges: DateRange[],
        scraperConfiguration: ScraperConfiguration,
        triggeredBy: TriggeredBy,
        shadowModeTask?: ShadowModeTask
    ): Promise<Run<ScrapeRunContext>> {
        const sourceAccount = await this.getSourceAccount(scraperConfiguration);
        const commonParams: CommonParams = await this.getCommonParams(source, sourceAccount.sessionPath, shadowModeTask);

        const scrapeParams: Omit<ScrapeParams, 'dateFrom' | 'dateTo'> = {
            ...commonParams,
            skuToIgnore: scraperConfiguration.skuToIgnore,
            credentials: sourceAccountToScrapeCredentials(source, sourceAccount)
        };

        const loginParams: LoginParams = {
            ...commonParams,
            credentials: sourceAccountToCredentials(source, sourceAccount)
        };

        const checkSessionParams: CheckSessionParams = {...commonParams};

        const {updateStatus, updateLastScrapeDate} = shadowModeTask ? getShadowModeStatusHelpers(shadowModeTask) : getStatusHelpers(this.storage, scraperConfiguration);

        return new ScrapeRun(
            {
                mainDir: this.mainDir,
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                checkSessionParams,
                loginParams,
                scrapeParams,
                updateStatus,
                updateLastScrapeDate,
                dateRanges
            },
            {
                priority: getSourcePriority(source),
                context: {
                    source,
                    command: Command.SCRAPE,
                    shadowModeTask,
                    numberOfDaysToScrape: sumDaysInDateRanges(dateRanges),
                    dateRangesToProcess: dateRanges,
                    forceRunStart: false,
                    triggeredBy
                }
            }
        );
    }

    async createGetSourceSideOrganizationsRun(
        sourceConfiguration: ScraperConfiguration,
        triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON
    ): Promise<Run<BaseContext, SourceSideOrganization[]>> {
        const sourceAccount = await this.getSourceAccount(sourceConfiguration);
        const commonParams: CommonParams = await this.getCommonParams(sourceConfiguration.source, sourceAccount.sessionPath);
        return new Run<BaseContext, SourceSideOrganization[]>([new GetSourceSideOrganizationsJob(this.binaryProxy, commonParams)], {
            context: {source: sourceConfiguration.source, forceRunStart: false, command: Command.GET_SOURCE_SIDE_ORGANIZATIONS, triggeredBy}
        });
    }

    async createGetManualLoginDataRun(source: Source, triggeredBy: TriggeredBy = TriggeredBy.USER_VIA_ELECTRON): Promise<Run<BaseContext, ManualLoginDetailsResult>> {
        const commonParams: CommonParams = await this.getCommonParams(source, undefined);
        return new Run<BaseContext, ManualLoginDetailsResult>([new GetManualLoginDataJob(this.binaryProxy, commonParams)], {
            context: {source, forceRunStart: true, command: Command.GET_MANUAL_LOGIN_DETAILS, triggeredBy}
        });
    }
    //TODO: make shadowModeTask required as we always pass it
    async createShadowModeLoginRun(source: Source, scraperConfiguration: ScraperConfiguration, shadowModeTask?: ShadowModeTask): Promise<Run<BaseContext, LoginResult>> {
        const sourceAccount = await this.getSourceAccount(scraperConfiguration);

        const loginParams: LoginParams = {
            ...(await this.getCommonParams(source, sourceAccount.sessionPath, shadowModeTask)),
            credentials: sourceAccountToCredentials(source, sourceAccount)
        };

        return new LoginRun(
            {
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                loginParams
            },
            {
                context: {
                    source,
                    command: Command.LOGIN,
                    forceRunStart: true,
                    triggeredBy: TriggeredBy.SHADOW_TASK,
                    shadowModeTask
                }
            }
        );
    }

    async createLoginWithCredentialsRun(source: Source, credentials: Credentials, sessionPath: string, triggeredBy: TriggeredBy): Promise<Run<BaseContext, LoginResult>> {
        const commonParams: CommonParams = await this.getCommonParams(source, sessionPath);
        const loginParams: LoginParams = {...commonParams, credentials};

        return new LoginRun(
            {
                dependenciesManager: this.dependenciesManager,
                binaryProxy: this.binaryProxy,
                s2Client: this.s2Client,
                loginParams
            },
            {
                context: {
                    source,
                    command: Command.LOGIN,
                    forceRunStart: true,
                    triggeredBy
                }
            }
        );
    }

    async createCheckSessionRun(source: Source, sessionPath: string, triggeredBy: TriggeredBy): Promise<Run<BaseContext, CheckSessionResult>> {
        const commonParams: CommonParams = await this.getCommonParams(source, sessionPath);
        const loginParams: LoginParams = {...commonParams, credentials: undefined};
        const checkSessionParams: CheckSessionParams = {...commonParams};

        // eslint-disable-next-line @typescript-eslint/no-empty-function
        const updateStatus: UpdateStatus = async (_newStatus: ScraperConfigurationStatus, _additionalParams?: any): Promise<void> => {};

        return new Run<BaseContext, CheckSessionResult>([new EnsureValidSessionJob(this.binaryProxy, checkSessionParams, loginParams, updateStatus)], {
            context: {source, forceRunStart: true, command: Command.CHECK_SESSION, triggeredBy}
        });
    }

    async getCommonParams(source: Source, sessionPath: string | undefined, shadowModeTask?: ShadowModeTask): Promise<CommonParams> {
        const sessionPath_ = shadowModeTask && sessionPath ? await copySessionFileForShadowRun(sessionPath) : sessionPath;

        const forcedExecPaths = shadowModeTask ? this.dependenciesManager.getExecPathsForShadowRun(shadowModeTask) : {};

        return {
            ...forcedExecPaths,
            source,
            sessionPath: sessionPath_,
            screenshotAndHtmlDumpPath: await this.storage.getScreenshotAndHtmlDumpDirPath(),
            featureFlags: getFeatureFlags()
        };
    }

    async getSourceAccount(scraperConfig: ScraperConfiguration): Promise<SourceAccount> {
        const sourceAccount = await this.storage.getSourceAccount(scraperConfig.sourceAccountId);
        if (!sourceAccount) {
            throw new Error('Linked source account not found.');
        }
        return sourceAccount;
    }
}
