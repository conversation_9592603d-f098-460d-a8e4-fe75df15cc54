import {errorType} from '../configurations/errorType';
import {BinaryProxy, LoginParams} from '../dependencies/BinaryProxy';
import {DependenciesManager} from '../dependencies/DependenciesManager';
import {LoginResult} from '../processes/types/resultTypes';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import * as telemetry from '../telemetry/telemetry';
import {removeFileOrDirectory} from '../utils/fileUtils';
import {LoginRunContext} from './jobs/context';
import {LoginJob} from './jobs/LoginJob';
import {SyncDependenciesJob} from './jobs/SyncDependenciesJob';
import {Run} from './Run';

export class LoginRun extends Run<LoginRunContext, LoginResult> {
    constructor(
        dependencies: {
            dependenciesManager: DependenciesManager;
            binaryProxy: BinaryProxy;
            loginParams: LoginParams;
            s2Client: iScraperServiceClient;
        },
        options: {
            context?: Omit<LoginRunContext, 'operationId' | 'isShadowRun'>;
            onScheduled?: () => Promise<void>;
            onStart?: () => Promise<void>;
            onSuccess?: () => Promise<void>;
        } = {}
    ) {
        super([new SyncDependenciesJob(dependencies.dependenciesManager), new LoginJob(dependencies.binaryProxy, dependencies.loginParams)], {
            ...options,
            onScheduled: async () => {
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'SCHEDULED',
                    isManualSession: false,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            },
            onStart: async () => {
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'STARTED',
                    isManualSession: false,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            },
            onSuccess: async () => {
                telemetry.trace(`Login successful for ${this.context.source}`);
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'CONFIGURED',
                    isManualSession: false,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            },
            onFail: async (e: any) => {
                telemetry.trace(`Login failed for ${this.context.source}`);
                telemetry.exception(e);
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'FAILED',
                    isManualSession: false,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined,
                    reason: e?.errorType ?? errorType.UNEXPECTED_ERROR
                });
            },
            onFinally: async () => {
                if (this.context.isShadowRun && dependencies.loginParams?.sessionPath) {
                    await removeFileOrDirectory(dependencies.loginParams.sessionPath, false);
                }
            },
            onKill: async () => {
                dependencies.s2Client.scheduleLoginStateChangedEvent({
                    source: this.context.source,
                    newState: 'STOPPED',
                    isManualSession: false,
                    triggeredBy: this.context.triggeredBy,
                    operationId: this.id,
                    accountIdentifier: undefined
                });
            }
        });
    }
}
