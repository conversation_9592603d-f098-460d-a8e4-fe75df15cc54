import {errorType} from '../../configurations/errorType';
import {ScraperConfigurationStatus} from '../../configurations/ScraperConfiguration';
import {BinaryProxy, LoginParams} from '../../dependencies/BinaryProxy';
import {LoginResult} from '../../processes/types';
import {Execution} from '../../processes/types/Execution';
import * as telemetry from '../../telemetry/telemetry';
import {Command} from '../../types';
import {BaseContext, LoginRunContext} from './context';
import {Job} from './Job';

export class LoginJob extends Job<LoginResult> {
    constructor(private binaryProxy: BinaryProxy, private loginParams: LoginParams) {
        super();
    }
    private execution?: Execution<LoginResult>;

    async execute(context: LoginRunContext): Promise<LoginResult> {
        if (!this.loginParams.credentials) {
            const prefix = `Login[${context.operationId}]` + (context.shadowModeTask ? ` ShadowMode[${context.shadowModeTask.id}][${context.shadowModeTask.label}]` : '');
            telemetry.trace(`${prefix}: newStatus: ${ScraperConfigurationStatus.ERROR} additionalParams: ${JSON.stringify({errorType: errorType.SESSION_EXPIRED})}`);
            throw new Error('Session expired');
        }
        this.execution = await this.binaryProxy.run(Command.LOGIN, this.loginParams, context.operationId);
        return this.execution.result;
    }
    async onSuccess(): Promise<void> {
        // Handle success
    }

    async onFail(): Promise<void> {
        // Handle failure
    }

    async kill(context: BaseContext): Promise<void> {
        await this.execution?.kill(context.operationId);
    }
}
