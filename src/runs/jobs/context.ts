import {ScrapeResult} from '../../processes/types';
import {TriggeredBy} from '../../telemetry/scraperService/scraperServiceEvents';
import {Command, DateRange, ShadowModeTask, Source} from '../../types';

export interface BaseContext {
    source: Source;
    command: Command;
    shadowModeTask?: ShadowModeTask;
    isShadowRun: boolean;
    forceRunStart: boolean;
    operationId: string;
    triggeredBy: TriggeredBy;
}

export interface ScrapeRunContext extends BaseContext {
    dateRangesToProcess: DateRange[];
    numberOfDaysToScrape: number;
    latestScrapeResults?: ScrapeResult[];
}

export type LoginRunContext = BaseContext;
