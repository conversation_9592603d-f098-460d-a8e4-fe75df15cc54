import {<PERSON>ronJob} from 'cron';
import {Schedule} from './Schedule';

type fn = (...params: any) => any;

let dailyScrapes: CronJob[] = [];
let stopCallback: fn | null;

export const startDailyCron = async (schedules: Schedule[], cronFn: fn, stopFn: fn) => {
    await stopDailyCron();
    dailyScrapes = schedules.map(({hours, minutes}) => new CronJob(`0 ${minutes} ${hours} * * *`, cronFn, null, true));
    stopCallback = stopFn;
};

export const stopDailyCron = async () => {
    dailyScrapes.map((job) => job.stop());
    if (stopCallback) {
        await stopCallback();
    }
    stopCallback = null;
    dailyScrapes = [];
};

function isToday(date: Date) {
    const now = new Date();
    return date.getDate() === now.getDate() && date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
}

export const isNextScheduleTomorrow = function (): boolean {
    const nextDates = dailyScrapes.map((job) => job.nextDate());
    return nextDates.every((date) => !isToday(date.toJSDate()));
};
