import {PublicScraperConfiguration} from '../publicInterfaces/PublicScraperConfiguration';
import {ScrapeRun} from '../runs/ScrapeRun';
import {ensureJsonSerializable} from '../utils/serialization';
import {ScraperConfiguration} from './ScraperConfiguration';

/**
 * Because of the use of instanceof ScrapeRun this method is renderer unsafe.
 * @param scraperConfiguration
 */
export function convertToPublicScraperConfiguration(scraperConfiguration: ScraperConfiguration): PublicScraperConfiguration {
    let activeRun: PublicScraperConfiguration['activeRun'] = undefined;

    if (scraperConfiguration.run) {
        activeRun = {
            percentageProgress: scraperConfiguration.run.progress,
            startDate: scraperConfiguration.run.startDate
        };
        if (scraperConfiguration.run instanceof ScrapeRun) {
            activeRun.numOfDataRangesToScrape = scraperConfiguration.run.dateRanges?.length;
            activeRun.numberOfDaysToScrape = scraperConfiguration.run.totalItems;
            activeRun.scrapedDays = scraperConfiguration.run.processedItems;
        }
    }

    const result: PublicScraperConfiguration = {
        id: scraperConfiguration.id,
        source: scraperConfiguration.source,
        activeRun,
        lastSuccessfulScrapeDate: scraperConfiguration.lastSuccessfulScrapeDate,
        status: scraperConfiguration.status,
        errorType: scraperConfiguration.errorType,
        previousStatus: scraperConfiguration.previousStatus,
        previousErrorType: scraperConfiguration.previousErrorType,
        sourceAccountId: scraperConfiguration.sourceAccountId,
        createdAt: scraperConfiguration.createdAt
    };

    return ensureJsonSerializable(result, 'Could not serialize configuration object');
}
