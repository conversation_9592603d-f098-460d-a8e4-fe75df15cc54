import {ScraperConfiguration} from '../../../configurations/ScraperConfiguration';
import {BinaryProxy} from '../../../dependencies/BinaryProxy';
import {DependenciesManager} from '../../../dependencies/DependenciesManager';
import {DependencyDetails} from '../../../dependencies/types';
import * as process from '../../../processes/spawnProcess';
import {RunFactory} from '../../../runs/RunFactory';
import {ElectronStorageImplementation} from '../../../storage/ElectronStorageImplementation';
import {Storage} from '../../../storage/Storage';
import {TriggeredBy} from '../../../telemetry/scraperService/scraperServiceEvents';
import {Source} from '../../../types';
import {mockScraperServiceClient, newAdapterWithStorage, processResultMock, scraperLibMainDirectory, testSourceAccountWithParams} from '../../utils/helpers';
import {mockDependencies, mockFeatureFlags} from '../../utils/scraperRunMocks';

let storage: Storage;
let dependenciesManager: DependenciesManager;
let factory: RunFactory;
let scraperConfiguration: ScraperConfiguration;

describe('RunFactory', () => {
    beforeEach(async () => {
        storage = new ElectronStorageImplementation(newAdapterWithStorage().adapter);
        scraperConfiguration = await storage.addScraperConfiguration({source: Source.PLAYSTATION_SALES}, testSourceAccountWithParams());
        dependenciesManager = new DependenciesManager(scraperLibMainDirectory);
        mockDependencies();
        mockFeatureFlags();
        const binaryProxy = new BinaryProxy(scraperLibMainDirectory, dependenciesManager);
        factory = new RunFactory(scraperLibMainDirectory, storage, dependenciesManager, binaryProxy, mockScraperServiceClient);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    test('Run can be created with a RunFactory', async () => {
        const run = await factory.createScrapeRun(Source.PLAYSTATION_SALES, [], scraperConfiguration, TriggeredBy.USER_VIA_ELECTRON);
        expect(run).toBeDefined();
    });

    test('Scrape run can be created with config override', async () => {
        const overrideAccountParams = testSourceAccountWithParams(undefined, 'OVERRIDE');
        const overrideConfiguration = await storage.addScraperConfiguration({source: Source.EPIC_SALES}, overrideAccountParams);
        const run = await factory.createScrapeRun(
            Source.PLAYSTATION_SALES,

            [],

            {
                source: Source.EPIC_SALES,
                sourceAccountId: overrideConfiguration.sourceAccountId,
                skuToIgnore: ['123']
            } as ScraperConfiguration,
            TriggeredBy.USER_VIA_ELECTRON
        );

        expect(run).toBeDefined();

        const jobs = run.jobs.filter((job) => job['checkSessionParams'] && job['loginParams']);
        expect(jobs.length).toBeGreaterThan(0);
        jobs.forEach((job) => {
            expect(job!['checkSessionParams']['sessionPath']).toBe(overrideAccountParams.sessionPath);
            expect(job!['loginParams']['sessionPath']).toBe(overrideAccountParams.sessionPath);
        });
    });

    test('Login run can be created with config override', async () => {
        const overrideAccountParams = testSourceAccountWithParams(undefined, 'OVERRIDE');
        const overrideConfiguration = await storage.addScraperConfiguration({source: Source.EPIC_SALES}, overrideAccountParams);
        const run = await factory.createShadowModeLoginRun(Source.PLAYSTATION_SALES, {
            source: Source.EPIC_SALES,
            sourceAccountId: overrideConfiguration.sourceAccountId,
            skuToIgnore: ['123']
        } as ScraperConfiguration);

        expect(run).toBeDefined();

        const jobs = run.jobs.filter((job) => job['loginParams']);
        expect(jobs.length).toBeGreaterThan(0);
        jobs.forEach((job) => {
            expect(job!['loginParams']['sessionPath']).toBe(overrideAccountParams.sessionPath);
        });
    });

    test('source side organizations run should have forceJobRun flag set to false', async () => {
        const sourceAccount = await storage.addSourceAccount({sessionPath: '123', accountIdentifier: '123'});
        const scraperConfig = await storage.addScraperConfiguration({source: Source.EPIC_SALES}, sourceAccount);
        const run = await factory.createGetSourceSideOrganizationsRun(scraperConfig);
        expect(run.context.forceRunStart).toBeFalsy();
    });

    test('scrape run should have forceJobRun flag set to false', async () => {
        const sourceAccount = await storage.addSourceAccount({sessionPath: '123', accountIdentifier: '123'});
        const scraperConfig = await storage.addScraperConfiguration({source: Source.EPIC_SALES}, sourceAccount);
        const run = await factory.createScrapeRun(Source.NINTENDO_SALES, [], scraperConfig, TriggeredBy.USER_VIA_ELECTRON);
        expect(run.context.forceRunStart).toBeFalsy();
    });

    test('Manual login details run should have forceJobRun flag set to true', async () => {
        const run = await factory.createGetManualLoginDataRun(Source.NINTENDO_SALES);
        expect(run.context.forceRunStart).toBeTruthy();
    });

    test('login run should have forceJobRun flag set to true', async () => {
        const sourceAccount = await storage.addSourceAccount({sessionPath: '123', accountIdentifier: '123'});
        const scraperConfiguration = await storage.addScraperConfiguration({source: Source.EPIC_SALES}, sourceAccount);

        const run = await factory.createShadowModeLoginRun(Source.NINTENDO_SALES, scraperConfiguration);

        expect(run.context.forceRunStart).toBeTruthy();
    });

    test('Run cannot be created for Source for which there is no sourceAccount in the storage', async () => {
        await expect(
            factory.createScrapeRun(Source.EPIC_SALES, [], {...scraperConfiguration, sourceAccountId: 'account-id-not-present'}, TriggeredBy.USER_VIA_ELECTRON)
        ).rejects.toThrow(new Error('Linked source account not found.'));
    });

    test('run is using proper binary path even if the dependencies are changed right before spawning it', async () => {
        const spawnProcessSpy = jest.spyOn(process, 'spawnProcess').mockImplementation(processResultMock(null));
        jest.spyOn(DependenciesManager, 'getDependency').mockResolvedValue({id: 'not-important-here'} as DependencyDetails);

        jest.spyOn(DependenciesManager.prototype, 'getDependencyExecPaths').mockResolvedValue({
            scrapersExecPath: 'dummy',
            chromiumExecPath: 'dummy'
        });

        const run = await factory.createScrapeRun(Source.PLAYSTATION_SALES, [], scraperConfiguration, TriggeredBy.USER_VIA_ELECTRON);

        jest.spyOn(DependenciesManager.prototype, 'getDependencyExecPaths').mockResolvedValue({
            scrapersExecPath: 'dummy_new',
            chromiumExecPath: 'dummy_new'
        });

        await run.executeAllJobs();

        expect(spawnProcessSpy).toBeCalledWith('dummy_new', expect.anything(), run.id);
    });

    test('RunFactory sets priorities based on the source', async () => {
        const run1 = await factory.createScrapeRun(Source.STEAM_SALES, [], scraperConfiguration, TriggeredBy.USER_VIA_ELECTRON);
        const run2 = await factory.createScrapeRun(Source.STEAM_WISHLIST_BALANCE, [], scraperConfiguration, TriggeredBy.USER_VIA_ELECTRON);
        const run3 = await factory.createScrapeRun(Source.STEAM_DISCOUNTS, [], scraperConfiguration, TriggeredBy.USER_VIA_ELECTRON);

        expect(run1.priority).toBe(0);
        expect(run2.priority).toBe(-1);
        expect(run3.priority).toBe(0);
    });
});
