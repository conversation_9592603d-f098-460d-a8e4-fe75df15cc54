import {BinaryProxy, LoginParams} from '../../../../dependencies/BinaryProxy';
import {LoginResult} from '../../../../processes/types';
import {Execution} from '../../../../processes/types/Execution';
import {LoginRunContext} from '../../../../runs/jobs/context';
import {LoginJob} from '../../../../runs/jobs/LoginJob';

function getBinaryProxyMock(result: LoginResult) {
    const killMock = jest.fn().mockResolvedValue(undefined);
    return {
        run: jest.fn().mockResolvedValue({
            kill: killMock as unknown as (operationId: string) => Promise<void>,
            result: Promise.resolve(result as unknown as LoginResult)
        } as Execution<LoginResult>),
        killMock
    } as unknown as BinaryProxy & {killMock: jest.Mock<Promise<void>, []>};
}

describe('LoginJob', () => {
    test('LoginJob.kill calls kill on the execution', async () => {
        const binaryProxy = getBinaryProxyMock({} as LoginResult);
        const loginJob = new LoginJob(binaryProxy, {credentials: {}} as LoginParams);
        const context = {operationId: 'fake'} as LoginRunContext;
        await loginJob.execute(context);

        await loginJob.kill(context);
        expect(binaryProxy.killMock).toHaveBeenCalled();
    });
});
