import {promises as fs} from 'fs';
import * as path from 'path';
import * as mockFs from 'mock-fs';
import * as api from '../../api/ScraperApi';
import {errorType} from '../../configurations/errorType';
import {ScraperConfiguration, ScraperConfigurationStatus} from '../../configurations/ScraperConfiguration';
import * as dailyCron from '../../cron/dailyScrapeCron';
import {DEFAULT_DAILY_SCHEDULE} from '../../cron/Schedule';
import {BinaryProxy} from '../../dependencies/BinaryProxy';
import {FeatureFlag} from '../../featureFlags/featureFlags';
import {Run} from '../../runs/Run';
import {ScraperLib} from '../../ScraperLib';
import {MicrosoftApiSetupData} from '../../types/MicrosoftApiSetupData';
import {Source} from '../../types/Source';
import {EncryptedJsonFile} from '../../utils/EncryptedJsonFile';
import {createDateRange} from '../utils/datesHelpers';
import {
    asyncVoidFunc,
    getFreshLib,
    mockScraperServiceClient,
    processRejectMock,
    processResultMock,
    scraperLibMainDirectory,
    testSourceAccountWithParams
} from '../utils/helpers';
import {mockDependencies, mockFeatureFlags} from '../utils/scraperRunMocks';

const cookies = [{a: 1}];

const source: Source = Source.EPIC_SALES;

const credentials = {user: 'user', password: 'password'};
const newCredentials = {user: 'user2', password: 'password2'};
const {accountIdentifier} = testSourceAccountWithParams();
const sessionsPath = path.join(scraperLibMainDirectory, 'sessions');

let scraperLib: ScraperLib;
let binRunSpy;
let getScrapDatesSpy;
let sendLoginStateChangedEventSpy: jest.SpyInstance;

describe('ScraperLib', () => {
    beforeEach(async () => {
        binRunSpy = jest.spyOn(BinaryProxy.prototype, 'run');
        getScrapDatesSpy = jest.spyOn(api, 'getScrapDates');
        binRunSpy.mockImplementation(processResultMock({hasScrapeBlockingIssues: false, id: accountIdentifier}));
        mockFs({
            [testSourceAccountWithParams().sessionPath]: ''
        });
        scraperLib = await getFreshLib();
        sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
        mockDependencies();
    });

    afterEach(async () => {
        await scraperLib.close();
        mockFs.restore();
        jest.useRealTimers();
        jest.resetAllMocks();
    });

    test('setupDailyCron should update daily schedule and start cron job', async () => {
        const startDailyCronSpy = jest.spyOn(dailyCron, 'startDailyCron').mockImplementation(asyncVoidFunc);
        const schedules = [{hours: 21, minutes: 37}];

        await scraperLib.setupDailyCron(schedules);
        expect(startDailyCronSpy).toBeCalledTimes(1);
        expect(await scraperLib.getDailyCronSchedule()).toEqual(schedules);
    });

    test('loginWithCookies saves session to file and sourceAccount and sends CONFIGURED event to s2', async () => {
        mockFs({
            [sessionsPath]: {}
        });
        binRunSpy.mockImplementation(processResultMock({hasScrapeBlockingIssues: false, id: 'not-important'}));
        const [newConfig] = await scraperLib.loginWithCookies(source, cookies);
        const sourceAccount = await scraperLib.getSourceAccount(newConfig.sourceAccountId!);

        const sessionFiles = await fs.readdir(sessionsPath);

        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: source,
            newState: 'CONFIGURED',
            isManualSession: true,
            operationId: expect.anything(),
            accountIdentifier: undefined,
            triggeredBy: 'USER_VIA_ELECTRON'
        });

        expect(sessionFiles).toHaveLength(1);
        expect(sourceAccount?.sessionPath).toMatch(sessionFiles[0]);
    });

    test('loginWithCookies replaces old session with a new session in sourceAccount linked in ScraperConfiguration', async () => {
        mockFs({
            [sessionsPath]: {}
        });
        for (const cookies of [[{a: 1}], [{b: 2}], [{c: 3}]].entries()) {
            const [config] = await scraperLib.loginWithCookies(source, cookies);
            const sourceAccount = await scraperLib.getSourceAccount(config.sourceAccountId!);
            const sessionFiles = await fs.readdir(sessionsPath);
            expect(sessionFiles).toHaveLength(1); // make sure there are no leftover session files
            expect(sessionFiles).toContain(sourceAccount!.sessionPath.split('/').pop());
            const encryptedJsonFile = new EncryptedJsonFile(sourceAccount!.sessionPath);
            const content = await encryptedJsonFile.load();
            expect(content.cookies).toEqual(cookies);
        }
    });

    test('loginWithCookies throws error after failed check-session and sends failed event to s2', async () => {
        binRunSpy.mockImplementation(processResultMock({hasScrapeBlockingIssues: true, id: 'not-important'}));
        await expect(scraperLib.loginWithCookies(source, cookies)).rejects.toEqual(Error(`Provided session doesn't allow to scrape`));

        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: source,
            newState: 'FAILED',
            isManualSession: true,
            operationId: expect.anything(),
            accountIdentifier: undefined,
            triggeredBy: 'USER_VIA_ELECTRON'
        });
    });

    test('loginWithCredentials creates new scraperConfiguration for a source if a such does not exists', async () => {
        const oldConfig = await scraperLib.getScraperConfiguration(source);
        expect(oldConfig).toBeUndefined();
        await scraperLib.loginWithCredentials(source, credentials);
        const newConfig = await scraperLib.getScraperConfiguration(source);
        expect(newConfig).toBeDefined();
        const sourceAccount = await scraperLib.getSourceAccount(newConfig!.sourceAccountId!);
        expect(sourceAccount?.cliParams).toEqual(expect.objectContaining(credentials));
    });

    test('loginWithCredentials adds a SourceAccount to existing scraperConfiguration in case scraperConfiguration did not have a SourceAccount earlier', async () => {
        const config = await scraperLib.addScraperConfiguration({source}, testSourceAccountWithParams());
        await scraperLib.deleteSourceAccount(config.sourceAccountId!);

        await scraperLib.loginWithCredentials(source, newCredentials);
        const refreshedConfig = await scraperLib.getScraperConfiguration(source);
        expect(refreshedConfig).toBeDefined();
        const sourceAccount = await scraperLib.getSourceAccount(refreshedConfig!.sourceAccountId!);
        expect(sourceAccount).toEqual(expect.objectContaining({accountIdentifier, cliParams: newCredentials}));
    });

    test('loginWithCredentials updates a SourceAccount linked in proper source scraperConfiguration', async () => {
        await scraperLib.addScraperConfiguration({source}, testSourceAccountWithParams());

        await scraperLib.loginWithCredentials(source, newCredentials);
        const refreshedConfig = await scraperLib.getScraperConfiguration(source);
        expect(refreshedConfig).toBeDefined();
        expect(refreshedConfig!.sourceAccountId).toBeTruthy();
        const sourceAccount = await scraperLib.getSourceAccount(refreshedConfig!.sourceAccountId!);
        expect(sourceAccount).toEqual(expect.objectContaining({accountIdentifier, cliParams: newCredentials}));
    }, ********);

    test('add ScraperConfiguration to related source after adding feature flag', async () => {
        const features: FeatureFlag[] = [`${Source.NINTENDO_FREE_TO_PLAY}_scrapers`, `${Source.NINTENDO_PREORDERS}_scrapers`, `${Source.NINTENDO_WII_DS_SALES}_scrapers`];
        await scraperLib.addScraperConfiguration({source: Source.NINTENDO_SALES}, testSourceAccountWithParams());
        expect((await scraperLib.listScraperConfigurations()).length).toBe(3);
        mockFeatureFlags(features);
        expect((await scraperLib.listScraperConfigurations()).length).toBe(3 + features.length);
    });

    test('filter ScraperConfiguration after removing feature flag', async () => {
        const features: FeatureFlag[] = [`${Source.NINTENDO_FREE_TO_PLAY}_scrapers`, `${Source.NINTENDO_PREORDERS}_scrapers`, `${Source.NINTENDO_WII_DS_SALES}_scrapers`];
        mockFeatureFlags(features);
        await scraperLib.addScraperConfiguration({source: Source.NINTENDO_SALES}, testSourceAccountWithParams());
        expect((await scraperLib.listScraperConfigurations()).length).toBe(3 + features.length);
        mockFeatureFlags();
        expect((await scraperLib.listScraperConfigurations()).length).toBe(3);
    });

    test('addCredentialsForSourceAccount updates credentials in linked sourceAccount', async () => {
        const config = await scraperLib.addScraperConfiguration({source}, testSourceAccountWithParams());
        await scraperLib.getSourceAccount(config.sourceAccountId!);
        await scraperLib.setCredentialsForSourceAccount(config.sourceAccountId!, newCredentials);
        const updatedSourceAccount = await scraperLib.getSourceAccount(config.sourceAccountId!);
        expect(updatedSourceAccount).toEqual(expect.objectContaining({accountIdentifier, cliParams: newCredentials}));
    });

    test('addCredentialsForSourceAccount should not update credentials sourceAccount if login fails', async () => {
        const config = await scraperLib.addScraperConfiguration({source}, testSourceAccountWithParams(credentials));
        await scraperLib.getSourceAccount(config.sourceAccountId!);
        binRunSpy.mockImplementation(processRejectMock(new Error()));
        await expect(scraperLib.setCredentialsForSourceAccount(config.sourceAccountId!, newCredentials)).rejects.toThrow();
        const updatedSourceAccount = await scraperLib.getSourceAccount(config.sourceAccountId!);
        expect(updatedSourceAccount).toEqual(expect.objectContaining({accountIdentifier, cliParams: credentials}));
    });

    test('addSkuToIgnoreForConfiguration should update config', async () => {
        const expectSkuToIgnore = ['1', '2'];
        const config = await scraperLib.addScraperConfiguration({source}, testSourceAccountWithParams());
        await scraperLib.addSkuToIgnoreForConfiguration(config.source, expectSkuToIgnore);
        const updatedConfig = await scraperLib.getScraperConfiguration(config.source);
        expect(updatedConfig?.skuToIgnore).toBe(expectSkuToIgnore);
    });

    test('configureMicrosoftSales should update SourceAccount', async () => {
        const apiSetupData: MicrosoftApiSetupData[] = [
            {
                shouldDownload: true,
                companyName: 'companyName',
                index: 0,
                clientId: 'clientId',
                clientSecret: 'clientSecret',
                tenantId: 'tenantId'
            }
        ];
        const config = await scraperLib.addScraperConfiguration({source: Source.MICROSOFT_SALES}, testSourceAccountWithParams());
        const sourceAccount = await scraperLib.getSourceAccount(config.sourceAccountId!);
        await scraperLib.configureMicrosoftSales(apiSetupData);
        const updatedSourceAccount = await scraperLib.getSourceAccount(config.sourceAccountId!);
        expect(updatedSourceAccount?.cliParams).toStrictEqual({apiSetupData});
        expect(updatedSourceAccount).toEqual({...sourceAccount, cliParams: {apiSetupData}});
    });
    // I'm not sure yet why, but when this test run, it breaks another test. Probably some problem with mocks.
    test.skip('scrapeSource should populate lastSuccessfulScrapeDate after successful scrape', async () => {
        getScrapDatesSpy.mockImplementation(async () => [createDateRange()]);
        mockDependencies();
        const config = await scraperLib.addScraperConfiguration({source}, testSourceAccountWithParams());
        expect(config.lastSuccessfulScrapeDate).toBeUndefined();
        const startDate = new Date();
        await scraperLib.scrapeSource(config.source);

        const updatedConfig = await scraperLib.getScraperConfiguration(config.source);
        expect(updatedConfig?.status).toBe(ScraperConfigurationStatus.CONFIGURED);
        expect(updatedConfig?.lastSuccessfulScrapeDate?.getTime()).toBeGreaterThanOrEqual(startDate.getTime());
        expect(updatedConfig?.lastSuccessfulScrapeDate?.getTime()).toBeLessThanOrEqual(new Date().getTime());
    });

    test('scrapeSource should update config to VALID if source is up to date', async () => {
        getScrapDatesSpy.mockImplementation(async () => []);
        const config = await scraperLib.addScraperConfiguration({source}, testSourceAccountWithParams());
        await scraperLib.scrapeSource(config.source);
        const updatedConfig = await scraperLib.getScraperConfiguration(config.source);
        expect(updatedConfig?.status).toBe(ScraperConfigurationStatus.CONFIGURED);
        expect(updatedConfig?.lastSuccessfulScrapeDate).toBeDefined();
        expect(updatedConfig?.lastSuccessfulScrapeDate?.getDate() === new Date().getDate());
    });

    test('getDailyCronSchedule should return default schedule', async () => {
        expect(await scraperLib.getDailyCronSchedule()).toEqual([DEFAULT_DAILY_SCHEDULE]);
    });

    test('getSourcesBySourceAccountId should return all sources linked to sourceAccount', async () => {
        mockDependencies();
        await scraperLib.loginWithCredentials(Source.META_QUEST_SALES, credentials);
        await scraperLib.loginWithCredentials(Source.EPIC_SALES, credentials);
        await scraperLib.loginWithCredentials(Source.STEAM_SALES, credentials);

        const {id: sourceAccountId} = (await scraperLib.getSourceAccountBySource(Source.META_QUEST_SALES))!;
        const metaSources = await scraperLib.getSourcesBySourceAccountId(sourceAccountId);
        expect(metaSources.sort()).toEqual([Source.META_QUEST_SALES, Source.META_RIFT_SALES].sort());

        const {id: sourceAccountId2} = (await scraperLib.getSourceAccountBySource(Source.STEAM_SALES))!;
        const steamSources = await scraperLib.getSourcesBySourceAccountId(sourceAccountId2);
        expect(steamSources.sort()).toEqual([Source.STEAM_SALES, Source.STEAM_WISHLISTS].sort());
    });

    test('listScraperConfigurations only returns configuration which match PublicScraperConfiguration interface', async () => {
        const someDate = new Date('2020-01-01');
        const configurationWithAllFields: ScraperConfiguration = {
            id: '1',
            source: Source.EPIC_SALES,
            skuToIgnore: ['1', '2'],
            sourceSideOrganizationsToScrape: ['1', '2'],
            sourceAccountId: '1',
            status: ScraperConfigurationStatus.CONFIGURED,
            errorType: errorType.UNEXPECTED_ERROR,
            previousStatus: ScraperConfigurationStatus.SCHEDULED,
            previousErrorType: errorType.DEPENDENCIES_SYNC_ERROR,
            run: {title: 'mocked run', startDate: someDate, progress: 0, extraField: 'injected field that should not be in public config'} as any as Run,
            lastSuccessfulScrapeDate: someDate,
            createdAt: someDate,
            extraField: 'injected field that should not be in public config'
        } as any as ScraperConfiguration;

        // @ts-expect-error we really want to mock this.
        scraperLib.getScraperConfigurations = () => Promise.resolve([configurationWithAllFields]);

        const publicConfig = await scraperLib.listScraperConfigurations();
        expect(publicConfig).toStrictEqual([
            {
                id: '1',
                source: Source.EPIC_SALES,
                activeRun: {
                    percentageProgress: 0,
                    startDate: someDate
                },
                lastSuccessfulScrapeDate: expect.any(Date),
                status: ScraperConfigurationStatus.CONFIGURED,
                errorType: errorType.UNEXPECTED_ERROR,
                previousStatus: ScraperConfigurationStatus.SCHEDULED,
                previousErrorType: errorType.DEPENDENCIES_SYNC_ERROR,
                sourceAccountId: '1',
                createdAt: someDate
            }
        ]);
    });
});
