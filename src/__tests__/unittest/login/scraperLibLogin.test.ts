import * as path from 'path';
import * as mockFs from 'mock-fs';
import {BinaryProxy} from '../../../dependencies/BinaryProxy';
import {LoginResult} from '../../../processes/types';
import {credentialsToSourceAccount} from '../../../runs/credentials';
import {ScraperLib} from '../../../ScraperLib';
import {Source} from '../../../types/Source';
import {getFreshLib, processResultMock, scraperLibMainDirectory, testSourceAccountWithParams} from '../../utils/helpers';
import {mockDependencies, mockFeatureFlags} from '../../utils/scraperRunMocks';

const inputCredentials = {user: 'username', password: 'password'};
const inputCookies = [];

const loginResult: LoginResult = {hasScrapeBlockingIssues: false, id: 'username'};

const methods = [
    ['loginWithCredentials', inputCredentials],
    ['loginWithCookies', inputCookies]
];
let scraperLib: ScraperLib;

describe.each(methods)('%s', (method: any, inputArgs: any) => {
    beforeEach(async () => {
        mockFeatureFlags();
        mockDependencies();
        const binRunSpy = jest.spyOn(BinaryProxy.prototype, 'run');
        binRunSpy.mockImplementation(processResultMock(loginResult));
        const sessionsPath = path.join(scraperLibMainDirectory, 'sessions');
        mockFs({
            [sessionsPath]: {},
            [testSourceAccountWithParams().sessionPath]: ''
        });
        scraperLib = await getFreshLib();
    });

    afterEach(async () => {
        mockFs.restore();
        jest.resetAllMocks();
        await scraperLib.close();
    });

    it('should create single configuration for source without related sources', async () => {
        expect(await scraperLib.listScraperConfigurations()).toHaveLength(0);
        const configurations = await scraperLib[method](Source.EPIC_SALES, inputArgs);
        expect(configurations).toHaveLength(1);
    });

    it('should create two scraper configurations with the same source account when called with source that have related source', async () => {
        expect(await scraperLib.listScraperConfigurations()).toHaveLength(0);

        const configurations = await scraperLib[method](Source.STEAM_SALES, inputArgs);

        expect(configurations).toHaveLength(2);

        expect(configurations[0].source).toBe(Source.STEAM_SALES);
        expect(configurations[1].source).toBe(Source.STEAM_WISHLISTS);

        expect(configurations[0].sourceAccountId).toEqual(configurations[1].sourceAccountId);
    });

    it('should create new source account, update scraper config and create new scraper config for related source', async () => {
        const sourceAccount = await scraperLib.addSourceAccount(
            testSourceAccountWithParams({
                ...credentialsToSourceAccount(Source.STEAM_SALES, inputCredentials)
            })
        );

        await scraperLib.addScraperConfiguration({source: Source.STEAM_SALES, sourceAccountId: sourceAccount.id});
        const configurations = await scraperLib[method](Source.STEAM_SALES, inputArgs);

        expect(configurations).toHaveLength(2);
        expect(configurations[0].source).toBe(Source.STEAM_SALES);
        expect(configurations[1].source).toBe(Source.STEAM_WISHLISTS);

        // source account id should be new
        expect(configurations[0].sourceAccountId).not.toEqual(sourceAccount.id);

        // source account id should be the same
        expect(configurations[0].sourceAccountId).toEqual(configurations[1].sourceAccountId);

        const newSourceAccount = await scraperLib.getSourceAccountBySource(Source.STEAM_SALES);

        // source account has been updated with new session
        expect(newSourceAccount?.sessionPath).not.toEqual(sourceAccount?.sessionPath);

        const deletedSourceAccount = await scraperLib.getSourceAccount(sourceAccount.id);
        expect(deletedSourceAccount).not.toBeDefined();
    });

    it('should create new source account, create new scraper config and update scraper config for related source', async () => {
        const oldSourceAccount = await scraperLib.addSourceAccount(
            testSourceAccountWithParams({
                ...credentialsToSourceAccount(Source.STEAM_SALES, inputCredentials)
            })
        );

        await scraperLib.addScraperConfiguration({source: Source.STEAM_SALES, sourceAccountId: oldSourceAccount.id});
        const configurations = await scraperLib[method](Source.STEAM_WISHLISTS, inputArgs);

        expect(configurations).toHaveLength(2);
        expect(configurations[0].sourceAccountId).not.toEqual(oldSourceAccount.id);
        expect(configurations[1].sourceAccountId).not.toEqual(oldSourceAccount.id);
        expect(configurations[1].sourceAccountId).toEqual(configurations[1].sourceAccountId);
    });
});
